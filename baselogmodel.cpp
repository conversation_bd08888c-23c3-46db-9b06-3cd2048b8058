#include "baselogmodel.h"
#include <QColor>
#include <QBrush>
#include <QFont>
#include <QDateTime>
#include <QDebug>

BaseLogModel::BaseLogModel(QObject* parent)
    : QAbstractTableModel(parent)
{
    initializeColumnVisibility();
}

int BaseLogModel::columnCount(const QModelIndex& parent) const
{
    Q_UNUSED(parent)
    return static_cast<int>(ColumnCount);
}

QVariant BaseLogModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole) {
        return getColumnName(section);
    }
    return QVariant();
}

void BaseLogModel::setColumnVisible(Column column, bool visible)
{
    if (column >= 0 && column < ColumnCount) {
        QMutexLocker locker(&m_mutex);
        m_columnVisibility[static_cast<int>(column)] = visible;
    }
}

bool BaseLogModel::isColumnVisible(Column column) const
{
    if (column >= 0 && column < ColumnCount) {
        QMutexLocker locker(&m_mutex);
        return m_columnVisibility[static_cast<int>(column)];
    }
    return false;
}

QVector<bool> BaseLogModel::getColumnVisibility() const
{
    QMutexLocker locker(&m_mutex);
    return m_columnVisibility;
}

void BaseLogModel::setColumnVisibility(const QVector<bool>& visibility)
{
    QMutexLocker locker(&m_mutex);
    if (visibility.size() == static_cast<int>(ColumnCount)) {
        m_columnVisibility = visibility;
    }
}

int BaseLogModel::mapToActualColumn(int visibleColumn) const
{
    QMutexLocker locker(&m_mutex);
    int actualColumn = 0;
    int visibleCount = 0;
    
    for (int i = 0; i < static_cast<int>(ColumnCount); ++i) {
        if (m_columnVisibility[i]) {
            if (visibleCount == visibleColumn) {
                actualColumn = i;
                break;
            }
            visibleCount++;
        }
    }
    
    return actualColumn;
}

QString BaseLogModel::getColumnName(int column) const
{
    switch (column) {
        case TimestampColumn:
            return tr("时间戳");
        case LevelColumn:
            return tr("级别");
        case SourceColumn:
            return tr("来源");
        case MessageColumn:
            return tr("消息");
        case DetailsColumn:
            return tr("详情");
        default:
            return tr("未知");
    }
}

QVariant BaseLogModel::formatLogEntryData(const LogEntry& entry, int column, int role) const
{
    switch (role) {
        case Qt::DisplayRole:
            switch (column) {
                case TimestampColumn:
                    return entry.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz");
                case LevelColumn:
                    return entry.level;
                case SourceColumn:
                    return entry.source;
                case MessageColumn:
                    return entry.message;
                case DetailsColumn:
                    return entry.details;
                default:
                    return QVariant();
            }
            
        case Qt::ForegroundRole:
            // 根据日志级别设置文字颜色
            if (column == LevelColumn || column == MessageColumn) {
                QString level = entry.level.toLower();
                if (level.contains("error") || level.contains("错误")) {
                    return QBrush(QColor(220, 20, 60)); // 深红色
                } else if (level.contains("warn") || level.contains("警告")) {
                    return QBrush(QColor(255, 140, 0)); // 橙色
                } else if (level.contains("info") || level.contains("信息")) {
                    return QBrush(QColor(0, 100, 0)); // 深绿色
                } else if (level.contains("debug") || level.contains("调试")) {
                    return QBrush(QColor(128, 128, 128)); // 灰色
                }
            }
            return QVariant();
            
        case Qt::BackgroundRole:
            // 根据日志级别设置背景色
            {
                QString level = entry.level.toLower();
                if (level.contains("error") || level.contains("错误")) {
                    return QBrush(QColor(255, 240, 240)); // 浅红色背景
                } else if (level.contains("warn") || level.contains("警告")) {
                    return QBrush(QColor(255, 248, 220)); // 浅橙色背景
                }
            }
            return QVariant();
            
        case Qt::FontRole:
            // 错误和警告使用粗体
            {
                QString level = entry.level.toLower();
                if (level.contains("error") || level.contains("warn") || 
                    level.contains("错误") || level.contains("警告")) {
                    QFont font;
                    font.setBold(true);
                    return font;
                }
            }
            return QVariant();
            
        case Qt::ToolTipRole:
            // 提供详细的工具提示
            return QString("时间: %1\n级别: %2\n来源: %3\n消息: %4\n详情: %5")
                   .arg(entry.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
                   .arg(entry.level)
                   .arg(entry.source)
                   .arg(entry.message)
                   .arg(entry.details);
            
        default:
            return QVariant();
    }
}

void BaseLogModel::initializeColumnVisibility()
{
    m_columnVisibility.resize(static_cast<int>(ColumnCount));
    for (int i = 0; i < static_cast<int>(ColumnCount); ++i) {
        m_columnVisibility[i] = true; // 默认所有列可见
    }
}
